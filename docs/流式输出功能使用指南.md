# 流式输出功能使用指南

## 📖 概述

当贝AI Provider API测试工具的流式输出功能提供了完整的Server-Sent Events (SSE)支持，包括实时数据显示、连接监控、错误处理、数据导出和高级过滤等功能。

## ✨ 主要功能

### 🔄 增强的流式客户端
- **自动重连机制**：连接断开时自动尝试重新连接
- **智能超时处理**：支持连接超时和响应超时配置
- **详细错误分类**：网络错误、超时错误、解析错误等
- **性能监控**：实时统计消息速率、数据传输量等
- **心跳检测**：定期检查连接状态

### 📊 实时监控面板
- **连接状态监控**：显示当前连接状态和持续时间
- **性能统计**：消息数量、数据量、传输速率等
- **错误统计**：错误次数、重连次数等
- **活动日志**：详细的连接和消息活动记录
- **可视化图表**：性能趋势图表（可扩展）

### 🔍 消息过滤和搜索
- **内容搜索**：支持关键词搜索和正则表达式
- **类型过滤**：按消息类型筛选（文本、进度、卡片等）
- **时间范围**：按时间范围筛选消息
- **实时过滤**：过滤条件实时生效
- **过滤统计**：显示过滤结果统计

### 📤 数据导出功能
- **多种格式**：支持JSON、CSV、TXT、HTML格式
- **灵活配置**：可选择导出内容和格式选项
- **过滤导出**：支持导出过滤后的数据
- **元数据支持**：可包含时间戳、类型等元数据

## 🚀 快速开始

### 1. 启动流式请求

1. 在API测试工具中选择支持流式响应的接口
2. 配置请求参数（确保包含`stream=true`参数）
3. 切换到"流式响应"标签页
4. 点击"开始流式请求"按钮

```javascript
// 示例请求配置
{
  "method": "POST",
  "url": "/api/chat/completions",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer your-api-key"
  },
  "body": {
    "messages": [
      {"role": "user", "content": "你好"}
    ],
    "stream": true
  }
}
```

### 2. 监控连接状态

流式请求开始后，监控面板会自动显示：

- **连接状态**：显示当前连接状态（连接中、已连接、重连中等）
- **基本统计**：连接时长、消息数量、数据量
- **详细监控**：点击"显示详情"查看更多统计信息

### 3. 使用过滤功能

1. 点击"显示过滤器"展开过滤面板
2. 设置过滤条件：
   - **内容搜索**：输入关键词或正则表达式
   - **消息类型**：选择要显示的消息类型
   - **时间范围**：选择时间范围或自定义时间
3. 点击"应用过滤"或实时生效

### 4. 导出数据

1. 点击工具栏中的"📤 导出数据"按钮
2. 在导出对话框中配置：
   - **导出格式**：选择JSON、CSV、TXT或HTML
   - **内容过滤**：选择要导出的消息类型
   - **导出选项**：是否包含元数据和样式信息
3. 点击"导出"按钮下载文件

## 🔧 高级配置

### 流式客户端配置

```javascript
const client = new EnhancedStreamClient({
  // 连接配置
  connectionTimeout: 15000,    // 连接超时时间（毫秒）
  responseTimeout: 60000,      // 响应超时时间（毫秒）
  heartbeatInterval: 30000,    // 心跳间隔（毫秒）
  
  // 重试配置
  enableRetry: true,           // 是否启用自动重试
  maxRetries: 5,               // 最大重试次数
  retryDelay: 2000,            // 重试延迟（毫秒）
  retryBackoff: 2,             // 重试延迟倍数
  
  // 数据处理配置
  enableDataValidation: true,  // 是否启用数据验证
  maxMessageSize: 1024 * 1024, // 最大消息大小（字节）
  
  // 监控配置
  enableMetrics: true,         // 是否启用性能监控
  metricsInterval: 1000        // 监控数据更新间隔（毫秒）
});
```

### 监控器配置

监控器会自动收集以下性能指标：

- `connectionTime`: 连接建立时间
- `totalMessages`: 总消息数量
- `totalBytes`: 总数据量
- `messagesPerSecond`: 消息速率
- `bytesPerSecond`: 数据传输速率
- `errors`: 错误次数
- `reconnections`: 重连次数

## 📋 消息类型和样式

### 支持的消息类型

| 类型 | 描述 | 样式特征 |
|------|------|----------|
| `text` | 正式回答内容 | 绿色边框，正常字体 |
| `progress` | 联网搜索进度 | 蓝色边框，进度指示 |
| `card` | 搜索结果卡片 | 主色边框，卡片样式 |
| `thinking` | AI思考过程 | 黄色边框，斜体字 |
| `error` | 错误信息 | 红色边框，错误样式 |
| `unknown` | 未知类型 | 灰色边框，默认样式 |

### 消息格式示例

```json
{
  "content_type": "text",
  "content": "这是AI的回答内容",
  "created_at": 1640995200000,
  "id": "msg_123"
}
```

## 🔍 搜索和过滤

### 搜索功能

1. **关键词搜索**：输入关键词进行内容匹配
2. **正则表达式**：启用正则表达式进行高级搜索
3. **大小写敏感**：控制搜索是否区分大小写

### 过滤选项

1. **消息类型过滤**：
   - 所有类型
   - 文本消息
   - 进度消息
   - 卡片消息
   - 思考消息
   - 错误消息

2. **时间范围过滤**：
   - 所有时间
   - 最近1分钟
   - 最近5分钟
   - 最近1小时
   - 自定义范围

### 过滤示例

```javascript
// 搜索包含"API"的文本消息
filter.currentFilters = {
  search: 'API',
  type: 'text',
  timeRange: 'last-hour'
};
filter.applyFilters();
```

## 📤 数据导出

### 导出格式

#### JSON格式
```json
{
  "exportTime": "2024-01-01T12:00:00.000Z",
  "totalMessages": 10,
  "messages": [
    {
      "id": 1,
      "content": "消息内容",
      "timestamp": "2024-01-01T12:00:00.000Z",
      "type": "text",
      "metadata": {
        "messageId": "msg_1",
        "contentType": "text",
        "size": 12
      }
    }
  ]
}
```

#### CSV格式
```csv
ID,时间戳,类型,内容,大小
1,2024-01-01T12:00:00.000Z,text,"消息内容",12
```

#### HTML格式
生成包含样式的HTML文档，保留消息的视觉效果。

## 🚨 错误处理

### 错误类型

| 错误类型 | 描述 | 处理方式 |
|----------|------|----------|
| `network_error` | 网络连接错误 | 自动重试 |
| `timeout_error` | 连接或响应超时 | 重新建立连接 |
| `parse_error` | 数据解析错误 | 记录错误，继续处理 |
| `connection_error` | 连接建立失败 | 根据重试配置处理 |
| `server_error` | 服务器错误 | 显示错误信息 |

### 错误恢复

1. **自动重连**：网络错误时自动尝试重新连接
2. **指数退避**：重试延迟逐渐增加
3. **错误记录**：所有错误都会被记录到监控系统
4. **用户通知**：重要错误会通过通知系统告知用户

## 🎯 最佳实践

### 1. 连接管理
- 合理设置超时时间，避免过长等待
- 启用自动重试，提高连接稳定性
- 监控连接状态，及时发现问题

### 2. 性能优化
- 对于大量消息，使用过滤功能减少显示内容
- 定期清空消息历史，避免内存占用过多
- 合理设置监控更新间隔

### 3. 数据管理
- 重要的流式数据及时导出保存
- 使用过滤功能快速定位特定消息
- 利用搜索功能查找历史内容

### 4. 错误处理
- 关注错误统计，分析连接质量
- 根据错误类型调整重试策略
- 保存错误日志用于问题排查

## 🔧 故障排除

### 常见问题

1. **连接无法建立**
   - 检查网络连接
   - 验证API端点URL
   - 确认认证信息正确

2. **消息显示异常**
   - 检查消息格式是否正确
   - 验证content_type字段
   - 查看浏览器控制台错误

3. **过滤不生效**
   - 确认过滤条件设置正确
   - 检查正则表达式语法
   - 验证时间范围设置

4. **导出失败**
   - 检查浏览器下载权限
   - 确认有可导出的数据
   - 查看控制台错误信息

### 调试技巧

1. **开启详细日志**：在浏览器控制台查看详细的连接和消息日志
2. **使用监控面板**：通过监控面板查看连接状态和性能指标
3. **检查网络面板**：在浏览器开发者工具中查看网络请求
4. **测试简单请求**：先用简单的请求测试连接是否正常

## 📚 API参考

### EnhancedStreamClient

```javascript
// 创建客户端
const client = new EnhancedStreamClient(options);

// 连接到流式端点
await client.connect(url, headers, callbacks);

// 断开连接
client.disconnect();

// 获取状态
const state = client.getState();

// 获取性能指标
const metrics = client.getMetrics();

// 更新配置
client.updateConfig(newConfig);
```

### StreamMonitor

```javascript
// 创建监控器
const monitor = new StreamMonitor(containerId);

// 更新连接状态
monitor.updateConnectionState(state);

// 记录消息
monitor.recordMessage(messageData);

// 记录错误
monitor.recordError(error);

// 重置数据
monitor.reset();
```

### StreamFilter

```javascript
// 创建过滤器
const filter = new StreamFilter(containerId);

// 应用过滤条件
filter.applyFilters();

// 重置过滤条件
filter.resetFilters();

// 获取过滤结果
const filtered = filter.getFilteredMessages();
```

### StreamExporter

```javascript
// 创建导出器
const exporter = new StreamExporter();

// 收集消息数据
const messages = exporter.collectStreamMessages();

// 导出为JSON
exporter.exportAsJSON(messages);

// 导出为CSV
exporter.exportAsCSV(messages);
```

## 🤝 贡献指南

如果您想为流式输出功能贡献代码或报告问题，请：

1. 查看现有的测试用例了解功能实现
2. 遵循代码规范和注释标准
3. 添加相应的测试用例
4. 更新相关文档

## 🎮 示例和演示

### 基本使用示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>流式输出功能演示</title>
    <link rel="stylesheet" href="../public/api-tester/css/style.css">
</head>
<body>
    <div class="stream-viewer">
        <div class="stream-toolbar">
            <button id="start-demo" class="btn btn-small">开始演示</button>
            <button id="stop-demo" class="btn btn-small btn-secondary">停止</button>
        </div>
        <div id="stream-monitor-container"></div>
        <div id="stream-filter-container"></div>
        <div class="stream-content">
            <div id="stream-messages" class="stream-messages"></div>
        </div>
    </div>

    <script src="../public/api-tester/js/enhanced-stream-client.js"></script>
    <script src="../public/api-tester/js/stream-monitor.js"></script>
    <script src="../public/api-tester/js/stream-filter.js"></script>
    <script src="../public/api-tester/js/stream-exporter.js"></script>

    <script>
        // 演示代码
        const monitor = new StreamMonitor('stream-monitor-container');
        const filter = new StreamFilter('stream-filter-container');
        const exporter = new StreamExporter();

        document.getElementById('start-demo').addEventListener('click', startDemo);

        function startDemo() {
            // 模拟流式消息
            const messages = [
                { content_type: 'progress', content: '开始搜索...', created_at: Date.now() },
                { content_type: 'progress', content: '搜索进度: 30%', created_at: Date.now() + 1000 },
                { content_type: 'card', content: '找到相关结果', created_at: Date.now() + 2000 },
                { content_type: 'thinking', content: '正在分析结果...', created_at: Date.now() + 3000 },
                { content_type: 'text', content: '这是最终的回答内容', created_at: Date.now() + 4000 }
            ];

            messages.forEach((msg, index) => {
                setTimeout(() => {
                    addMessage(msg);
                    monitor.recordMessage({ data: msg, size: JSON.stringify(msg).length });
                }, index * 1000);
            });
        }

        function addMessage(data) {
            const container = document.getElementById('stream-messages');
            const messageElement = document.createElement('div');
            messageElement.className = `sse-message ${data.content_type}`;
            messageElement.innerHTML = `
                <div class="sse-message-header">
                    <span class="sse-message-type">${data.content_type}</span>
                    <span class="sse-message-timestamp">${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="sse-message-content">${data.content}</div>
            `;
            container.appendChild(messageElement);
        }
    </script>
</body>
</html>
```

### 高级配置示例

```javascript
// 创建具有自定义配置的流式客户端
const client = new EnhancedStreamClient({
    connectionTimeout: 20000,
    responseTimeout: 120000,
    maxRetries: 10,
    retryDelay: 3000,
    retryBackoff: 1.5,
    enableMetrics: true,
    metricsInterval: 500
});

// 连接到流式端点
await client.connect('wss://api.example.com/stream', {
    'Authorization': 'Bearer your-token',
    'Accept': 'text/event-stream'
}, {
    onOpen: () => console.log('连接已建立'),
    onMessage: (data) => handleMessage(data),
    onError: (error) => handleError(error),
    onClose: () => console.log('连接已关闭'),
    onStateChange: (newState) => updateUI(newState),
    onMetrics: (metrics) => updateMetrics(metrics)
});
```

## 📄 许可证

本功能遵循项目的整体许可证协议。
