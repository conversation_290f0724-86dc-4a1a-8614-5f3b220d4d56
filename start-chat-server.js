#!/usr/bin/env node

/**
 * 启动聊天服务器脚本
 * 使用现有的HTTP API控制器
 */

const express = require('express');
const path = require('path');
const cors = require('cors');

// 导入编译后的控制器和服务
let <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ModelsController, ModelService;
let chatController = null;
let modelsController = null;

try {
  // 尝试导入编译后的控制器和服务
  const chatModule = require('./dist/server/controllers/chat-controller.js');
  const modelsModule = require('./dist/server/controllers/models-controller.js');
  const modelServiceModule = require('./dist/server/services/model-service.js');

  ChatController = chatModule.ChatController;
  ModelsController = modelsModule.ModelsController;
  ModelService = modelServiceModule.ModelService;

  // 初始化服务和控制器
  const modelService = new ModelService();
  chatController = new ChatController();
  modelsController = new ModelsController();

  // 注入依赖（如果需要）
  if (chatController.setModelService) {
    chatController.setModelService(modelService);
  }
  if (modelsController.setModelService) {
    modelsController.setModelService(modelService);
  }

  console.log('✅ 成功加载编译后的控制器');
} catch (error) {
  console.warn('⚠️  无法加载编译后的控制器，使用测试模式');
  console.warn('错误:', error.message);
  console.warn('堆栈:', error.stack);

  // 降级到测试模式
  ChatController = null;
  ModelsController = null;
}

const app = express();
const port = process.env.PORT || 3003;

// 中间件
app.use(cors({
  origin: '*',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path}`);
  next();
});

// 静态文件服务
app.use('/chat', express.static(path.join(__dirname, 'public/chat')));
app.use('/assets', express.static(path.join(__dirname, 'public/assets')));

if (chatController && modelsController) {
  // 使用真实的控制器
  console.log('🚀 使用真实的HTTP API控制器');

  // API路由
  app.get('/api/models', (req, res) => modelsController.getModels(req, res));
  app.get('/api/models/:modelId', (req, res) => modelsController.getModelById(req, res));
  app.post('/api/chat', (req, res) => chatController.chat(req, res));

  // OpenAI兼容路由
  app.get('/v1/models', (req, res) => modelsController.getModelsOpenAI(req, res));
  app.get('/v1/models/:modelId', (req, res) => modelsController.getModelByIdOpenAI(req, res));
  
} else {
  // 使用测试模式的API
  console.log('🧪 使用测试模式API');
  
  // 模型列表API
  app.get('/api/models', (req, res) => {
    res.json({
      success: true,
      data: {
        defaultModel: 'deepseek-r1',
        models: [
          {
            id: 'deepseek-r1',
            name: 'DeepSeek-R1最新版',
            description: '专注逻辑推理与深度分析，擅长解决复杂问题，提供精准决策支持',
            options: [
              { name: '深度思考', value: 'deep', enabled: true, selected: true },
              { name: '联网搜索', value: 'online', enabled: true, selected: false }
            ],
            recommended: true,
            pinned: true,
            badge: 'HOT'
          },
          {
            id: 'gpt-4o',
            name: 'GPT-4o',
            description: '多模态大语言模型，支持文本、图像等多种输入',
            options: [
              { name: '深度思考', value: 'deep', enabled: false, selected: false },
              { name: '联网搜索', value: 'online', enabled: true, selected: false }
            ],
            recommended: false,
            pinned: false
          }
        ],
        total: 2
      },
      requestId: 'req_' + Date.now(),
      timestamp: Date.now()
    });
  });
  
  // 聊天API
  app.post('/api/chat', (req, res) => {
    const { stream, model, options = {} } = req.body;
    
    console.log('收到聊天请求:', { model, stream, options });
    
    if (stream) {
      // 流式响应
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('X-Request-ID', 'req_' + Date.now());
      
      // 根据选项生成不同的回复内容
      let messages = ['这', '是', '一', '个', '测', '试', '回', '复', '。'];
      
      if (options.deep_thinking) {
        messages = ['让', '我', '仔', '细', '思', '考', '一', '下', '...', '\n\n', '经', '过', '深', '度', '分', '析', '，', '我', '认', '为', '这', '个', '问', '题', '需', '要', '从', '多', '个', '角', '度', '来', '考', '虑', '。'];
      }
      
      if (options.online_search) {
        messages = ['正', '在', '搜', '索', '最', '新', '信', '息', '...', '\n\n', '根', '据', '最', '新', '数', '据', '显', '示', '，', '当', '前', '的', '情', '况', '是', '...'];
      }
      
      if (options.deep_thinking && options.online_search) {
        messages = ['正', '在', '联', '网', '搜', '索', '并', '深', '度', '思', '考', '...', '\n\n', '综', '合', '分', '析', '结', '果', '：', '这', '是', '一', '个', '复', '杂', '的', '问', '题', '，', '需', '要', '结', '合', '最', '新', '信', '息', '进', '行', '全', '面', '分', '析', '。'];
      }
      
      let index = 0;
      const conversationId = 'conv_' + Date.now();
      const messageId = 'msg_' + Date.now();
      
      const interval = setInterval(() => {
        if (index < messages.length) {
          const chunk = {
            id: messageId,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: model || 'deepseek-r1',
            choices: [{
              index: 0,
              delta: { content: messages[index] },
              finish_reason: null
            }],
            conversation_id: conversationId,
            message_id: messageId
          };
          
          res.write(`data: ${JSON.stringify(chunk)}\n\n`);
          index++;
        } else {
          const finalChunk = {
            id: messageId,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: model || 'deepseek-r1',
            choices: [{ index: 0, delta: {}, finish_reason: 'stop' }]
          };
          
          res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
          res.write('data: [DONE]\n\n');
          res.end();
          clearInterval(interval);
        }
      }, 100);
      
    } else {
      // 普通响应
      let content = '这是一个测试回复。';
      
      if (options.deep_thinking) {
        content = '经过深度思考分析，我认为这个问题需要从多个角度来考虑...';
      }
      
      if (options.online_search) {
        content = '根据最新搜索到的信息，当前的情况是...';
      }
      
      if (options.deep_thinking && options.online_search) {
        content = '结合最新信息和深度分析，我的综合建议是...';
      }
      
      res.json({
        success: true,
        data: {
          message: {
            role: 'assistant',
            content: content,
            id: 'msg_' + Date.now(),
            timestamp: Date.now()
          },
          conversation_id: 'conv_' + Date.now(),
          message_id: 'msg_' + Date.now(),
          model: model || 'deepseek-r1',
          finish_reason: 'stop'
        },
        requestId: 'req_' + Date.now(),
        timestamp: Date.now()
      });
    }
  });
}

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: Date.now(),
      mode: ChatController ? 'production' : 'test'
    }
  });
});

// 根路径重定向
app.get('/', (req, res) => {
  res.redirect('/chat');
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: '服务器内部错误'
    },
    timestamp: Date.now()
  });
});

// 启动服务器
app.listen(port, '0.0.0.0', () => {
  console.log('🎉 聊天服务器启动成功!');
  console.log(`🌐 服务器地址: http://localhost:${port}`);
  console.log(`💬 聊天界面: http://localhost:${port}/chat`);
  console.log(`🔧 模式: ${ChatController ? '生产模式' : '测试模式'}`);
  console.log('🚀 服务器已就绪，等待请求...');
});
