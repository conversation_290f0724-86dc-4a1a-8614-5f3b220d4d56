/**
 * HTTP API 服务器启动入口
 * 启动当贝AI Provider HTTP API服务器
 */

import { createApp, setupGracefulShutdown, AppConfig } from './app';

/**
 * 服务器配置
 */
interface ServerConfig extends AppConfig {
  /** 服务器端口 */
  port?: number;
  /** 服务器主机 */
  host?: string;
}

/**
 * 启动服务器
 *
 * @param config 服务器配置
 */
export async function startServer(config: ServerConfig = {}): Promise<void> {
  try {
    // 从环境变量或配置中获取参数
    const {
      port = parseInt(process.env['PORT'] || '3000', 10),
      host = process.env['HOST'] || '0.0.0.0',
      debug = process.env['NODE_ENV'] === 'development',
      timeout = parseInt(process.env['TIMEOUT'] || '30000', 10),
      slowThreshold = parseInt(process.env['SLOW_THRESHOLD'] || '1000', 10),
      sizeThreshold = parseInt(process.env['SIZE_THRESHOLD'] || '1048576', 10), // 1MB
      cors = {
        origin: process.env['CORS_ORIGIN'] || '*',
        credentials: process.env['CORS_CREDENTIALS'] === 'true'
      }
    } = config;

    console.log('🔧 服务器配置:');
    console.log(`   端口: ${port}`);
    console.log(`   主机: ${host}`);
    console.log(`   调试模式: ${debug ? '启用' : '禁用'}`);
    console.log(`   请求超时: ${timeout}ms`);
    console.log(`   慢请求阈值: ${slowThreshold}ms`);
    console.log(`   大请求阈值: ${(sizeThreshold / 1024 / 1024).toFixed(1)}MB`);
    console.log(`   CORS源: ${cors.origin}`);

    // 创建应用
    const app = createApp({
      debug,
      timeout,
      slowThreshold,
      sizeThreshold,
      cors
    });

    // 启动服务器
    const server = app.listen(port, host, () => {
      console.log('🎉 当贝AI Provider HTTP API服务器启动成功!');
      console.log(`🌐 服务器地址: http://${host}:${port}`);
      console.log(`📖 API文档: http://${host}:${port}/api/info`);
      console.log(`💚 健康检查: http://${host}:${port}/health`);
      console.log(`📊 使用统计: http://${host}:${port}/stats`);
      console.log('');
      console.log('🚀 服务器已就绪，等待请求...');
    });

    // 设置优雅关闭
    setupGracefulShutdown(server);

    // 监听服务器错误
    server.on('error', (error: any) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${port} 已被占用，请尝试其他端口`);
      } else if (error.code === 'EACCES') {
        console.error(`❌ 没有权限绑定到端口 ${port}`);
      } else {
        console.error('❌ 服务器启动失败:', error);
      }
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 主函数逻辑已移至 start-server.js 脚本

// 导出启动函数供其他模块使用
export { createApp, setupGracefulShutdown } from './app';
export type { AppConfig, ServerConfig };

// 注意：直接运行请使用 start-server.js 脚本

// 如果直接通过 ts-node/node 运行本文件（而不是被其他模块引用），则自动启动服务器
// 这样可兼容 `npm run server` / `npm run server:dev` 等脚本直接执行本文件的场景
// 注意：保留导出以供 start-server.js 或其他模块按需调用
if (require.main === module) {
  // 这里不解析命令行参数，优先从环境变量读取（PORT/HOST/NODE_ENV 等）
  // 方便在不同环境下通过环境变量控制端口、主机和调试模式
  startServer().catch((error) => {
    console.error('❌ 启动失败:', error);
    process.exit(1);
  });
}

