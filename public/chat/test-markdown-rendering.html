<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown渲染测试 - 当贝AI聊天界面</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/themes.css">
    
    <!-- 外部库 -->
    <!-- Mermaid图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- KaTeX数学公式库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    
    <!-- Prism.js语法高亮库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-secondary);
        }
        
        .test-title {
            font-size: 1.5em;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-color);
        }
        
        .test-input {
            width: 100%;
            min-height: 100px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        
        .test-button {
            margin: 10px 0;
            padding: 10px 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            opacity: 0.9;
        }
        
        .test-output {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Markdown渲染功能测试</h1>
        <p>测试增强的Markdown渲染功能，包括Mermaid图表、数学公式、语法高亮等。</p>
        
        <!-- 基础Markdown测试 -->
        <div class="test-section">
            <div class="test-title">基础Markdown测试</div>
            <textarea class="test-input" id="basic-input" placeholder="输入Markdown内容...">
# 标题测试

## 二级标题

### 三级标题

**粗体文本** 和 *斜体文本* 以及 ~~删除线文本~~

这是一个 `行内代码` 示例。

> 这是一个引用块
> 可以包含多行内容

- 无序列表项1
- 无序列表项2
  - 嵌套列表项
  - 另一个嵌套项

1. 有序列表项1
2. 有序列表项2

[链接示例](https://example.com)

| 表头1 | 表头2 | 表头3 |
|-------|-------|-------|
| 单元格1 | 单元格2 | 单元格3 |
| 数据1 | 数据2 | 数据3 |

---

分隔线上方和下方的内容。
            </textarea>
            <button class="test-button" onclick="testBasicMarkdown()">渲染基础Markdown</button>
            <div class="test-output message-bubble" id="basic-output"></div>
        </div>
        
        <!-- 代码块测试 -->
        <div class="test-section">
            <div class="test-title">代码块和语法高亮测试</div>
            <textarea class="test-input" id="code-input" placeholder="输入包含代码块的Markdown...">
```javascript
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// 计算斐波那契数列
console.log(fibonacci(10));
```

```python
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quicksort(left) + middle + quicksort(right)

# 测试快速排序
print(quicksort([3, 6, 8, 10, 1, 2, 1]))
```
            </textarea>
            <button class="test-button" onclick="testCodeBlocks()">渲染代码块</button>
            <div class="test-output message-bubble" id="code-output"></div>
        </div>
        
        <!-- Mermaid图表测试 -->
        <div class="test-section">
            <div class="test-title">Mermaid图表测试</div>
            <textarea class="test-input" id="mermaid-input" placeholder="输入Mermaid图表代码...">
```mermaid
graph TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主页]
    B -->|否| D[显示登录页]
    D --> E[用户输入]
    E --> F{验证成功?}
    F -->|是| C
    F -->|否| G[显示错误]
    G --> D
    C --> H[结束]
```

```mermaid
sequenceDiagram
    participant 用户
    participant 前端
    participant 后端
    participant 数据库
    
    用户->>前端: 发送请求
    前端->>后端: API调用
    后端->>数据库: 查询数据
    数据库-->>后端: 返回结果
    后端-->>前端: 响应数据
    前端-->>用户: 显示结果
```
            </textarea>
            <button class="test-button" onclick="testMermaid()">渲染Mermaid图表</button>
            <div class="test-output message-bubble" id="mermaid-output"></div>
        </div>
        
        <!-- 数学公式测试 -->
        <div class="test-section">
            <div class="test-title">数学公式测试</div>
            <textarea class="test-input" id="math-input" placeholder="输入包含数学公式的内容...">
这是行内数学公式：$E = mc^2$，爱因斯坦的质能方程。

这是块级数学公式：

$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$

二次方程的解：

$$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$$

矩阵示例：

$$\begin{pmatrix}
a & b \\
c & d
\end{pmatrix}
\begin{pmatrix}
x \\
y
\end{pmatrix}
=
\begin{pmatrix}
ax + by \\
cx + dy
\end{pmatrix}$$
            </textarea>
            <button class="test-button" onclick="testMath()">渲染数学公式</button>
            <div class="test-output message-bubble" id="math-output"></div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/markdown.js"></script>
    
    <script>
        // 测试函数
        async function testBasicMarkdown() {
            const input = document.getElementById('basic-input').value;
            const output = document.getElementById('basic-output');
            await markdownRenderer.renderComplete(input, output);
        }
        
        async function testCodeBlocks() {
            const input = document.getElementById('code-input').value;
            const output = document.getElementById('code-output');
            await markdownRenderer.renderComplete(input, output);
        }
        
        async function testMermaid() {
            const input = document.getElementById('mermaid-input').value;
            const output = document.getElementById('mermaid-output');
            await markdownRenderer.renderComplete(input, output);
        }
        
        async function testMath() {
            const input = document.getElementById('math-input').value;
            const output = document.getElementById('math-output');
            await markdownRenderer.renderComplete(input, output);
        }
        
        // 页面加载完成后自动测试基础功能
        window.addEventListener('load', () => {
            console.log('Markdown渲染测试页面已加载');
            console.log('可用的外部库:');
            console.log('- Mermaid:', typeof mermaid !== 'undefined');
            console.log('- KaTeX:', typeof katex !== 'undefined');
            console.log('- Prism:', typeof Prism !== 'undefined');
        });
    </script>
</body>
</html>
