/* 当贝AI聊天界面 - 主题样式文件 */

/* 深色主题 */
[data-theme="dark"] {
  /* 颜色系统 */
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-active: #096dd9;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  
  /* 中性色 */
  --text-primary: #ffffff;
  --text-secondary: #a6a6a6;
  --text-tertiary: #737373;
  --text-quaternary: #525252;
  
  /* 背景色 */
  --bg-primary: #141414;
  --bg-secondary: #1f1f1f;
  --bg-tertiary: #262626;
  --bg-quaternary: #2f2f2f;
  
  /* 边框色 */
  --border-color: #434343;
  --border-color-light: #303030;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2), 0 1px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* 深色主题特定样式 */
[data-theme="dark"] .message.user .message-bubble {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

[data-theme="dark"] .message-bubble {
  background: var(--bg-secondary);
  border-color: var(--border-color-light);
}

[data-theme="dark"] .new-chat-btn {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

[data-theme="dark"] .new-chat-btn:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

[data-theme="dark"] .session-item.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

[data-theme="dark"] .input-wrapper {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .input-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

[data-theme="dark"] .model-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 深色主题下的对话选项样式 */
[data-theme="dark"] .option-item {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .option-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

[data-theme="dark"] .option-label:hover {
  background: var(--bg-tertiary);
}

[data-theme="dark"] .checkbox-custom {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .option-checkbox:checked + .checkbox-custom {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

[data-theme="dark"] .option-checkbox:disabled + .checkbox-custom {
  background: var(--bg-quaternary);
  border-color: var(--border-color);
}

/* 主题切换按钮状态 */
[data-theme="light"] .theme-toggle .icon-dark {
  display: none;
}

[data-theme="dark"] .theme-toggle .icon-light {
  display: none;
}

/* 滚动条深色主题 */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--text-quaternary);
}

/* 代码块样式 */
.message-bubble pre {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color-light);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  margin: var(--spacing-sm) 0;
  overflow-x: auto;
  font-family: var(--font-mono);
  font-size: 13px;
  line-height: 1.4;
}

.message-bubble code {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color-light);
  border-radius: var(--radius-sm);
  padding: 2px 6px;
  font-family: var(--font-mono);
  font-size: 13px;
}

.message-bubble pre code {
  background: transparent;
  border: none;
  padding: 0;
}

[data-theme="dark"] .message-bubble pre {
  background: var(--bg-quaternary);
  border-color: var(--border-color);
}

[data-theme="dark"] .message-bubble code {
  background: var(--bg-quaternary);
  border-color: var(--border-color);
}

[data-theme="dark"] .message.user .message-bubble pre,
[data-theme="dark"] .message.user .message-bubble code {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

/* 链接样式 */
.message-bubble a {
  color: var(--primary-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all var(--transition-fast);
}

.message-bubble a:hover {
  border-bottom-color: var(--primary-color);
}

.message.user .message-bubble a {
  color: rgba(255, 255, 255, 0.9);
  border-bottom-color: transparent;
}

.message.user .message-bubble a:hover {
  color: white;
  border-bottom-color: rgba(255, 255, 255, 0.7);
}

/* 表格样式 */
.message-bubble table {
  width: 100%;
  border-collapse: collapse;
  margin: var(--spacing-sm) 0;
  font-size: 13px;
}

.message-bubble th,
.message-bubble td {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color-light);
  text-align: left;
}

.message-bubble th {
  background: var(--bg-tertiary);
  font-weight: 600;
}

[data-theme="dark"] .message-bubble th,
[data-theme="dark"] .message-bubble td {
  border-color: var(--border-color);
}

[data-theme="dark"] .message-bubble th {
  background: var(--bg-quaternary);
}

/* 列表样式 */
.message-bubble ul,
.message-bubble ol {
  margin: var(--spacing-sm) 0;
  padding-left: var(--spacing-lg);
}

.message-bubble li {
  margin: var(--spacing-xs) 0;
  line-height: 1.5;
}

/* 引用样式 */
.message-bubble blockquote {
  margin: var(--spacing-sm) 0;
  padding: var(--spacing-sm) var(--spacing-md);
  border-left: 3px solid var(--primary-color);
  background: var(--bg-tertiary);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
  font-style: italic;
}

[data-theme="dark"] .message-bubble blockquote {
  background: var(--bg-quaternary);
}

.message.user .message-bubble blockquote {
  border-left-color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
}

/* 分隔线样式 */
.message-bubble hr {
  margin: var(--spacing-md) 0;
  border: none;
  height: 1px;
  background: var(--border-color-light);
}

[data-theme="dark"] .message-bubble hr {
  background: var(--border-color);
}

.message.user .message-bubble hr {
  background: rgba(255, 255, 255, 0.3);
}

/* 强调文本样式 */
.message-bubble strong {
  font-weight: 600;
}

.message-bubble em {
  font-style: italic;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --border-color-light: #666666;
    --text-tertiary: #333333;
  }
  
  [data-theme="dark"] {
    --border-color: #ffffff;
    --border-color-light: #999999;
    --text-tertiary: #cccccc;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .typing-dot {
    animation: none;
  }
  
  .spinner {
    animation: none;
    border-top-color: var(--primary-color);
  }
}

/* 打印样式 */
@media print {
  .app-header,
  .sidebar,
  .input-section,
  .settings-panel,
  .loading-overlay,
  .error-toast {
    display: none !important;
  }
  
  .chat-section {
    width: 100% !important;
  }
  
  .messages-container {
    padding: 0 !important;
  }
  
  .message-bubble {
    border: 1px solid #000 !important;
    background: #fff !important;
    color: #000 !important;
  }
}
