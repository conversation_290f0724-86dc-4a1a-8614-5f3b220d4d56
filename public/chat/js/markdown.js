/**
 * 当贝AI聊天界面 - Markdown渲染器
 * 提供Markdown格式的文本渲染功能
 */

class MarkdownRenderer {
  constructor() {
    this.codeBlockCounter = 0;
    this.mermaidCounter = 0;
    this.mathCounter = 0;

    // 初始化外部库
    this.initializeLibraries();
  }

  /**
   * 初始化外部渲染库
   */
  async initializeLibraries() {
    // 初始化Mermaid
    if (typeof mermaid !== 'undefined') {
      mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        fontFamily: 'inherit'
      });
    }

    // 初始化数学公式渲染器
    this.initMathRenderer();
  }

  /**
   * 初始化数学公式渲染器
   */
  initMathRenderer() {
    // 检查是否有MathJax或KaTeX可用
    if (typeof MathJax !== 'undefined') {
      this.mathRenderer = 'mathjax';
    } else if (typeof katex !== 'undefined') {
      this.mathRenderer = 'katex';
    } else {
      this.mathRenderer = 'simple';
    }
  }

  /**
   * 渲染Markdown文本
   * @param {string} text - 要渲染的Markdown文本
   * @returns {string} 渲染后的HTML
   */
  render(text) {
    if (!text) return '';

    let html = text;

    // 转义HTML特殊字符（除了我们要处理的Markdown语法）
    html = this.escapeHtml(html);

    // 处理数学公式（在代码块之前，避免冲突）
    html = this.renderMath(html);

    // 处理Mermaid图表（在代码块处理中）
    html = this.renderCodeBlocks(html);

    // 处理行内代码
    html = this.renderInlineCode(html);

    // 处理标题
    html = this.renderHeaders(html);

    // 处理粗体、斜体和删除线
    html = this.renderEmphasis(html);

    // 处理链接和图片
    html = this.renderLinks(html);
    html = this.renderImages(html);

    // 处理列表（支持嵌套）
    html = this.renderLists(html);

    // 处理引用
    html = this.renderBlockquotes(html);

    // 处理表格
    html = this.renderTables(html);

    // 处理分隔线
    html = this.renderHorizontalRules(html);

    // 处理换行
    html = this.renderLineBreaks(html);

    return html;
  }

  /**
   * 完整渲染（包含异步处理）
   * @param {string} text - 要渲染的Markdown文本
   * @param {HTMLElement} container - 容器元素
   */
  async renderComplete(text, container) {
    // 先进行基础渲染
    const html = this.render(text);
    container.innerHTML = html;

    // 异步处理Mermaid图表
    await this.processMermaidCharts(container);

    // 异步处理数学公式
    await this.processMathFormulas(container);

    // 应用语法高亮
    this.applySyntaxHighlighting(container);
  }

  /**
   * 转义HTML特殊字符
   * @param {string} text - 要转义的文本
   * @returns {string} 转义后的文本
   */
  escapeHtml(text) {
    const htmlEscapes = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
    };
    
    return text.replace(/[&<>"']/g, match => htmlEscapes[match]);
  }

  /**
   * 渲染代码块
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderCodeBlocks(text) {
    // 处理三个反引号的代码块
    return text.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
      const lang = (language || 'text').toLowerCase();
      const escapedCode = code.trim();

      // 特殊处理Mermaid图表
      if (lang === 'mermaid') {
        const mermaidId = `mermaid-${++this.mermaidCounter}`;
        return `<div class="mermaid-container">
          <div class="mermaid-chart" id="${mermaidId}" data-mermaid="${this.escapeHtml(escapedCode)}">
            <div class="mermaid-loading">正在渲染图表...</div>
          </div>
          <div class="mermaid-actions">
            <button class="mermaid-copy" onclick="copyMermaidCode('${mermaidId}')" title="复制Mermaid代码">
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
              </svg>
              复制代码
            </button>
          </div>
        </div>`;
      }

      // 普通代码块
      const codeId = `code-block-${++this.codeBlockCounter}`;
      return `<div class="code-block" data-language="${lang}">
        <div class="code-header">
          <span class="code-language">${lang}</span>
          <button class="code-copy" onclick="copyCodeBlock('${codeId}')" title="复制代码">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </svg>
          </button>
        </div>
        <pre id="${codeId}"><code class="language-${lang}" data-code="${this.escapeHtml(escapedCode)}">${escapedCode}</code></pre>
      </div>`;
    });
  }

  /**
   * 渲染行内代码
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderInlineCode(text) {
    return text.replace(/`([^`]+)`/g, '<code>$1</code>');
  }

  /**
   * 渲染标题
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderHeaders(text) {
    return text.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, content) => {
      const level = hashes.length;
      return `<h${level}>${content.trim()}</h${level}>`;
    });
  }

  /**
   * 渲染粗体、斜体和删除线
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderEmphasis(text) {
    // 删除线 ~~text~~
    text = text.replace(/~~([^~]+)~~/g, '<del>$1</del>');

    // 粗体 **text** 或 __text__
    text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    text = text.replace(/__([^_]+)__/g, '<strong>$1</strong>');

    // 斜体 *text* 或 _text_ (避免与粗体冲突)
    text = text.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '<em>$1</em>');
    text = text.replace(/(?<!_)_([^_]+)_(?!_)/g, '<em>$1</em>');

    return text;
  }

  /**
   * 渲染图片
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderImages(text) {
    // Markdown图片 ![alt](src "title")
    return text.replace(/!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)/g, (match, alt, src, title) => {
      const titleAttr = title ? ` title="${title}"` : '';
      return `<img src="${src}" alt="${alt}"${titleAttr} class="markdown-image" loading="lazy">`;
    });
  }

  /**
   * 渲染链接
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderLinks(text) {
    // Markdown链接 [text](url)
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
    
    // 自动链接
    text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
    
    return text;
  }

  /**
   * 渲染列表（支持嵌套）
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderLists(text) {
    // 处理有序列表（支持嵌套）
    text = this.renderOrderedLists(text);

    // 处理无序列表（支持嵌套）
    text = this.renderUnorderedLists(text);

    return text;
  }

  /**
   * 渲染有序列表
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderOrderedLists(text) {
    const lines = text.split('\n');
    let result = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i];
      const match = line.match(/^(\s*)(\d+)\.\s+(.+)$/);

      if (match) {
        const [, indent, num, content] = match;
        const level = indent.length;

        if (level === 0) {
          // 顶级列表项
          const listItems = this.collectListItems(lines, i, /^(\s*)(\d+)\.\s+(.+)$/);
          result.push(`<ol>${listItems.html}</ol>`);
          i = listItems.nextIndex;
        } else {
          result.push(line);
          i++;
        }
      } else {
        result.push(line);
        i++;
      }
    }

    return result.join('\n');
  }

  /**
   * 渲染无序列表
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderUnorderedLists(text) {
    const lines = text.split('\n');
    let result = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i];
      const match = line.match(/^(\s*)([-*+])\s+(.+)$/);

      if (match) {
        const [, indent] = match;
        const level = indent.length;

        if (level === 0) {
          // 顶级列表项
          const listItems = this.collectListItems(lines, i, /^(\s*)([-*+])\s+(.+)$/);
          result.push(`<ul>${listItems.html}</ul>`);
          i = listItems.nextIndex;
        } else {
          result.push(line);
          i++;
        }
      } else {
        result.push(line);
        i++;
      }
    }

    return result.join('\n');
  }

  /**
   * 收集列表项（支持嵌套）
   * @param {Array} lines - 文本行数组
   * @param {number} startIndex - 开始索引
   * @param {RegExp} pattern - 匹配模式
   * @returns {Object} 包含HTML和下一个索引
   */
  collectListItems(lines, startIndex, pattern) {
    let html = '';
    let i = startIndex;

    while (i < lines.length) {
      const line = lines[i];
      const match = line.match(pattern);

      if (match) {
        const [, indent, marker, content] = match;
        const level = indent.length;

        if (level === 0) {
          html += `<li>${content}</li>`;
          i++;
        } else {
          break;
        }
      } else if (line.trim() === '') {
        // 空行，继续
        i++;
      } else {
        break;
      }
    }

    return { html, nextIndex: i };
  }

  /**
   * 渲染引用
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderBlockquotes(text) {
    return text.replace(/^>\s+(.+)$/gm, '<blockquote>$1</blockquote>');
  }

  /**
   * 渲染表格
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderTables(text) {
    return text.replace(/^\|(.+)\|\n\|[-\s|:]+\|\n((\|.+\|\n?)+)/gm, (match, header, separator, rows) => {
      const headerCells = header.split('|').map(cell => `<th>${cell.trim()}</th>`).join('');
      const rowsHtml = rows.trim().split('\n').map(row => {
        const cells = row.split('|').slice(1, -1).map(cell => `<td>${cell.trim()}</td>`).join('');
        return `<tr>${cells}</tr>`;
      }).join('');
      
      return `<table>
        <thead><tr>${headerCells}</tr></thead>
        <tbody>${rowsHtml}</tbody>
      </table>`;
    });
  }

  /**
   * 渲染分隔线
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderHorizontalRules(text) {
    return text.replace(/^(---+|===+|\*\*\*+)$/gm, '<hr>');
  }

  /**
   * 渲染换行
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderLineBreaks(text) {
    // 两个空格加换行符转换为<br>
    text = text.replace(/  \n/g, '<br>\n');
    
    // 段落分隔（两个换行符）
    text = text.replace(/\n\n/g, '</p><p>');
    
    // 包装在段落标签中
    if (text && !text.startsWith('<')) {
      text = `<p>${text}</p>`;
    }
    
    return text;
  }

  /**
   * 渲染数学公式
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderMath(text) {
    // 块级数学公式 $$formula$$ (必须在行内公式之前处理)
    text = text.replace(/\$\$([^$]+)\$\$/g, (match, formula) => {
      const mathId = `math-block-${++this.mathCounter}`;
      return `<div class="math-block" id="${mathId}" data-formula="${this.escapeHtml(formula.trim())}">${this.escapeHtml(formula.trim())}</div>`;
    });

    // 行内数学公式 $formula$ (避免与块级公式冲突)
    text = text.replace(/(?<!\$)\$([^$\n]+)\$(?!\$)/g, (match, formula) => {
      const mathId = `math-inline-${++this.mathCounter}`;
      return `<span class="math-inline" id="${mathId}" data-formula="${this.escapeHtml(formula.trim())}">${this.escapeHtml(formula.trim())}</span>`;
    });

    return text;
  }

  /**
   * 清理HTML
   * @param {string} html - HTML字符串
   * @returns {string} 清理后的HTML
   */
  sanitizeHtml(html) {
    // 允许的标签
    const allowedTags = [
      'p', 'br', 'strong', 'em', 'code', 'pre', 'a', 'ul', 'ol', 'li',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'table', 'thead',
      'tbody', 'tr', 'th', 'td', 'hr', 'div', 'span'
    ];
    
    // 简单的HTML清理（生产环境建议使用专门的库如DOMPurify）
    const div = document.createElement('div');
    div.innerHTML = html;
    
    // 移除不允许的标签
    const allElements = div.querySelectorAll('*');
    allElements.forEach(el => {
      if (!allowedTags.includes(el.tagName.toLowerCase())) {
        el.replaceWith(...el.childNodes);
      }
    });
    
    return div.innerHTML;
  }

  /**
   * 处理Mermaid图表
   * @param {HTMLElement} container - 容器元素
   */
  async processMermaidCharts(container) {
    const mermaidCharts = container.querySelectorAll('.mermaid-chart');

    for (const chart of mermaidCharts) {
      try {
        const mermaidCode = chart.dataset.mermaid;
        if (mermaidCode && typeof mermaid !== 'undefined') {
          // 清除加载提示
          chart.innerHTML = '';

          // 渲染Mermaid图表
          const { svg } = await mermaid.render(chart.id + '-svg', mermaidCode);
          chart.innerHTML = svg;

          // 添加响应式样式
          const svgElement = chart.querySelector('svg');
          if (svgElement) {
            svgElement.style.maxWidth = '100%';
            svgElement.style.height = 'auto';
          }
        }
      } catch (error) {
        console.error('Mermaid渲染失败:', error);
        chart.innerHTML = `<div class="mermaid-error">图表渲染失败: ${error.message}</div>`;
      }
    }
  }

  /**
   * 处理数学公式
   * @param {HTMLElement} container - 容器元素
   */
  async processMathFormulas(container) {
    if (this.mathRenderer === 'mathjax' && typeof MathJax !== 'undefined') {
      await this.processMathJax(container);
    } else if (this.mathRenderer === 'katex' && typeof katex !== 'undefined') {
      await this.processKaTeX(container);
    }
  }

  /**
   * 使用MathJax处理数学公式
   * @param {HTMLElement} container - 容器元素
   */
  async processMathJax(container) {
    try {
      // 重新配置MathJax以处理新内容
      if (MathJax.typesetPromise) {
        await MathJax.typesetPromise([container]);
      } else if (MathJax.Hub) {
        // MathJax v2
        MathJax.Hub.Queue(['Typeset', MathJax.Hub, container]);
      }
    } catch (error) {
      console.error('MathJax渲染失败:', error);
    }
  }

  /**
   * 使用KaTeX处理数学公式
   * @param {HTMLElement} container - 容器元素
   */
  async processKaTeX(container) {
    const mathElements = container.querySelectorAll('.math-inline, .math-block');

    for (const element of mathElements) {
      try {
        const formula = element.dataset.formula;
        const isBlock = element.classList.contains('math-block');

        katex.render(formula, element, {
          displayMode: isBlock,
          throwOnError: false,
          errorColor: '#cc0000'
        });
      } catch (error) {
        console.error('KaTeX渲染失败:', error);
        element.innerHTML = `<span class="math-error">公式渲染失败</span>`;
      }
    }
  }

  /**
   * 应用语法高亮
   * @param {HTMLElement} container - 容器元素
   */
  applySyntaxHighlighting(container) {
    const codeBlocks = container.querySelectorAll('pre code[class*="language-"]');

    for (const block of codeBlocks) {
      // 如果有Prism.js可用
      if (typeof Prism !== 'undefined') {
        Prism.highlightElement(block);
      }
      // 如果有highlight.js可用
      else if (typeof hljs !== 'undefined') {
        hljs.highlightElement(block);
      }
      // 简单的语法高亮
      else {
        this.applySimpleSyntaxHighlighting(block);
      }
    }
  }

  /**
   * 简单的语法高亮（备用方案）
   * @param {HTMLElement} codeElement - 代码元素
   */
  applySimpleSyntaxHighlighting(codeElement) {
    const language = codeElement.className.match(/language-(\w+)/)?.[1];
    const code = codeElement.textContent;

    if (!language || !code) return;

    let highlightedCode = code;

    // 简单的关键字高亮
    const keywords = {
      javascript: ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'class', 'import', 'export'],
      python: ['def', 'class', 'if', 'else', 'elif', 'for', 'while', 'return', 'import', 'from', 'try', 'except'],
      java: ['public', 'private', 'class', 'interface', 'if', 'else', 'for', 'while', 'return', 'import', 'package'],
      css: ['color', 'background', 'margin', 'padding', 'border', 'width', 'height', 'display', 'position']
    };

    if (keywords[language]) {
      keywords[language].forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'g');
        highlightedCode = highlightedCode.replace(regex, `<span class="keyword">${keyword}</span>`);
      });
    }

    // 高亮字符串
    highlightedCode = highlightedCode.replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="string">$1$2$1</span>');

    // 高亮注释
    highlightedCode = highlightedCode.replace(/(\/\/.*$|\/\*[\s\S]*?\*\/)/gm, '<span class="comment">$1</span>');

    codeElement.innerHTML = highlightedCode;
  }
}

/**
 * 复制代码块内容
 * @param {string} blockId - 代码块ID
 */
function copyCodeBlock(blockId) {
  const codeBlock = document.getElementById(blockId);
  if (codeBlock) {
    const code = codeBlock.textContent;
    copyToClipboard(code).then(success => {
      if (success) {
        showToast('代码已复制到剪贴板', 'success', 2000);
      } else {
        showToast('复制失败，请手动选择复制', 'error', 3000);
      }
    });
  }
}

/**
 * 复制Mermaid代码
 * @param {string} chartId - 图表ID
 */
function copyMermaidCode(chartId) {
  const chart = document.getElementById(chartId);
  if (chart && chart.dataset.mermaid) {
    const code = chart.dataset.mermaid;
    copyToClipboard(code).then(success => {
      if (success) {
        showToast('Mermaid代码已复制到剪贴板', 'success', 2000);
      } else {
        showToast('复制失败，请手动选择复制', 'error', 3000);
      }
    });
  }
}

// 创建全局Markdown渲染器实例
const markdownRenderer = new MarkdownRenderer();
