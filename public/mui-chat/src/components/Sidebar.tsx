/**
 * 侧边栏组件
 * 包含会话列表、模型选择和聊天选项
 */

import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  IconButton,
  Typography,
  Button,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Tooltip,
  Paper,
} from '@mui/material';
import {
  Add as AddIcon,
  Chat as ChatIcon,
  Delete as DeleteIcon,
  Clear as ClearIcon,
  SmartToy as BotIcon,
  Star as StarIcon,
  Schedule as RecentIcon,
} from '@mui/icons-material';
import { ChatSession, ModelInfo, ChatOptions } from '../types/chat';

/**
 * 侧边栏组件属性
 */
interface SidebarProps {
  /** 会话列表 */
  sessions: ChatSession[];
  /** 当前会话 */
  currentSession: ChatSession | null;
  /** 模型列表 */
  models: ModelInfo[];
  /** 选中的模型 */
  selectedModel: string;
  /** 创建新会话回调 */
  onCreateNewSession: (model?: string) => void;
  /** 选择会话回调 */
  onSelectSession: (sessionId: string) => void;
  /** 删除会话回调 */
  onDeleteSession: (sessionId: string) => void;
  /** 清空当前会话回调 */
  onClearCurrentSession: () => void;
  /** 选择模型回调 */
  onSelectModel: (modelId: string) => void;
  /** 选项变化回调 */
  onOptionsChange: (options: ChatOptions) => void;
  /** 聊天选项 */
  chatOptions: ChatOptions;
}

/**
 * 侧边栏组件
 */
const Sidebar: React.FC<SidebarProps> = ({
  sessions,
  currentSession,
  models,
  selectedModel,
  onCreateNewSession,
  onSelectSession,
  onDeleteSession,
  onClearCurrentSession,
  onSelectModel,
  onOptionsChange,
  chatOptions,
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);

  /**
   * 处理删除会话
   */
  const handleDeleteClick = (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setSessionToDelete(sessionId);
    setDeleteDialogOpen(true);
  };

  /**
   * 确认删除会话
   */
  const handleConfirmDelete = () => {
    if (sessionToDelete) {
      onDeleteSession(sessionToDelete);
      setSessionToDelete(null);
    }
    setDeleteDialogOpen(false);
  };

  /**
   * 取消删除
   */
  const handleCancelDelete = () => {
    setSessionToDelete(null);
    setDeleteDialogOpen(false);
  };

  /**
   * 格式化时间
   */
  const formatTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return new Date(timestamp).toLocaleDateString('zh-CN');
  };

  /**
   * 获取模型显示信息
   */
  const getModelInfo = (modelId: string) => {
    return models.find(m => m.id === modelId) || {
      id: modelId,
      name: modelId,
      description: '未知模型'
    };
  };

  return (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.paper',
      }}
    >
      {/* 头部 */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          聊天会话
        </Typography>
        
        {/* 新建会话按钮 */}
        <Button
          fullWidth
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => onCreateNewSession()}
          sx={{
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 500,
          }}
        >
          新建对话
        </Button>
      </Box>

      {/* 模型选择 */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <FormControl fullWidth size="small">
          <InputLabel>选择模型</InputLabel>
          <Select
            value={selectedModel}
            label="选择模型"
            onChange={(e) => onSelectModel(e.target.value)}
            sx={{ borderRadius: 2 }}
          >
            {models.map((model) => (
              <MenuItem key={model.id} value={model.id}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                  <BotIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {model.name}
                    </Typography>
                    {model.description && (
                      <Typography variant="caption" color="text.secondary">
                        {model.description}
                      </Typography>
                    )}
                  </Box>
                  {model.recommended && (
                    <Chip
                      label="推荐"
                      size="small"
                      color="primary"
                      sx={{ height: 16, fontSize: '0.6rem' }}
                    />
                  )}
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {/* 当前会话操作 */}
      {currentSession && (
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="subtitle2" gutterBottom>
            当前对话
          </Typography>
          <Paper
            variant="outlined"
            sx={{
              p: 1.5,
              borderRadius: 2,
              bgcolor: 'primary.50',
              borderColor: 'primary.main',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <ChatIcon sx={{ fontSize: 16, color: 'primary.main' }} />
              <Typography variant="body2" sx={{ fontWeight: 500, flex: 1 }}>
                {currentSession.title}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                size="small"
                startIcon={<ClearIcon />}
                onClick={onClearCurrentSession}
                sx={{ 
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  minWidth: 'auto',
                  px: 1,
                }}
              >
                清空
              </Button>
            </Box>
          </Paper>
        </Box>
      )}

      {/* 会话列表 */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <Box sx={{ p: 1 }}>
          <Typography variant="subtitle2" sx={{ px: 1, py: 0.5, color: 'text.secondary' }}>
            历史对话
          </Typography>
        </Box>
        
        {sessions.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              p: 3,
              textAlign: 'center',
            }}
          >
            <ChatIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              暂无对话历史
            </Typography>
            <Typography variant="caption" color="text.disabled">
              点击"新建对话"开始聊天
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {sessions.map((session) => {
              const isSelected = currentSession?.id === session.id;
              const modelInfo = getModelInfo(session.model);
              
              return (
                <ListItem key={session.id} disablePadding>
                  <ListItemButton
                    selected={isSelected}
                    onClick={() => onSelectSession(session.id)}
                    sx={{
                      mx: 1,
                      mb: 0.5,
                      borderRadius: 2,
                      '&.Mui-selected': {
                        bgcolor: 'primary.50',
                        borderLeft: 3,
                        borderColor: 'primary.main',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <ChatIcon 
                        sx={{ 
                          fontSize: 18,
                          color: isSelected ? 'primary.main' : 'text.secondary'
                        }} 
                      />
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: isSelected ? 600 : 400,
                            color: isSelected ? 'primary.main' : 'text.primary',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {session.title}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                          <Typography variant="caption" color="text.secondary">
                            {formatTime(session.updatedAt)}
                          </Typography>
                          <Typography variant="caption" color="text.disabled">
                            •
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {modelInfo.name}
                          </Typography>
                        </Box>
                      }
                    />
                    
                    <Tooltip title="删除对话">
                      <IconButton
                        size="small"
                        onClick={(e) => handleDeleteClick(session.id, e)}
                        sx={{
                          opacity: 0.6,
                          '&:hover': {
                            opacity: 1,
                            color: 'error.main',
                          },
                        }}
                      >
                        <DeleteIcon sx={{ fontSize: 16 }} />
                      </IconButton>
                    </Tooltip>
                  </ListItemButton>
                </ListItem>
              );
            })}
          </List>
        )}
      </Box>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCancelDelete}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>删除对话</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除这个对话吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>取消</Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Sidebar;
