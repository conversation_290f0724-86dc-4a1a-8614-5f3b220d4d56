#!/usr/bin/env node

/**
 * 简单的服务器测试脚本
 */

const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const port = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 静态文件服务
app.use('/chat', express.static(path.join(__dirname, 'public/chat')));
app.use('/assets', express.static(path.join(__dirname, 'public/assets')));

// 简单的API端点
app.get('/api/models', (req, res) => {
  res.json({
    success: true,
    data: {
      defaultModel: 'deepseek-r1',
      models: [
        {
          id: 'deepseek-r1',
          name: 'DeepSeek-R1最新版',
          description: '专注逻辑推理与深度分析，擅长解决复杂问题，提供精准决策支持',
          options: [
            {
              name: '深度思考',
              value: 'deep',
              enabled: true,
              selected: true
            },
            {
              name: '联网搜索',
              value: 'online',
              enabled: true,
              selected: false
            }
          ],
          recommended: true,
          pinned: true,
          badge: 'HOT'
        },
        {
          id: 'gpt-4o',
          name: 'GPT-4o',
          description: '多模态大语言模型，支持文本、图像等多种输入',
          options: [
            {
              name: '深度思考',
              value: 'deep',
              enabled: false,
              selected: false
            },
            {
              name: '联网搜索',
              value: 'online',
              enabled: true,
              selected: false
            }
          ],
          recommended: false,
          pinned: false
        },
        {
          id: 'claude-3',
          name: 'Claude-3 Sonnet',
          description: '高质量的对话模型，擅长创作和分析',
          options: [
            {
              name: '深度思考',
              value: 'deep',
              enabled: true,
              selected: false
            },
            {
              name: '联网搜索',
              value: 'online',
              enabled: false,
              selected: false
            }
          ],
          recommended: false,
          pinned: false
        }
      ],
      total: 3
    },
    requestId: 'req_' + Date.now(),
    timestamp: Date.now()
  });
});

app.post('/api/chat', (req, res) => {
  const { stream, model, options = {} } = req.body;

  console.log('收到聊天请求:', { model, stream, options });

  if (stream) {
    // 流式响应
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('X-Request-ID', 'req_' + Date.now());

    // 根据选项生成不同的回复内容
    let messages = ['这', '是', '一', '个', '测', '试', '回', '复'];

    if (options.deep_thinking) {
      messages = ['让', '我', '仔', '细', '思', '考', '一', '下', '...', '\n\n', '经', '过', '深', '度', '分', '析', '，', '我', '认', '为', '...'];
    }

    if (options.online_search) {
      messages = ['正', '在', '搜', '索', '最', '新', '信', '息', '...', '\n\n', '根', '据', '最', '新', '数', '据', '显', '示', '...'];
    }

    if (options.deep_thinking && options.online_search) {
      messages = ['正', '在', '联', '网', '搜', '索', '并', '深', '度', '思', '考', '...', '\n\n', '综', '合', '分', '析', '结', '果', '：', '...'];
    }

    let index = 0;
    const conversationId = 'conv_' + Date.now();
    const messageId = 'msg_' + Date.now();

    const interval = setInterval(() => {
      if (index < messages.length) {
        const chunk = {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: model || 'deepseek-r1',
          choices: [{
            index: 0,
            delta: {
              content: messages[index]
            },
            finish_reason: null
          }],
          // 保留原始数据用于前端样式区分
          content_type: 'text',
          role: 'assistant',
          type: 'message',
          conversation_id: conversationId,
          message_id: messageId,
          request_id: 'req_' + Date.now()
        };

        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
        index++;
      } else {
        const finalChunk = {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: model || 'deepseek-r1',
          choices: [{
            index: 0,
            delta: {},
            finish_reason: 'stop'
          }]
        };

        res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
        res.write('data: [DONE]\n\n');
        res.end();
        clearInterval(interval);
      }
    }, 150);

  } else {
    // 普通响应
    const conversationId = 'conv_' + Date.now();
    const messageId = 'msg_' + Date.now();

    let content = '这是一个测试回复。';

    if (options.deep_thinking) {
      content = '经过深度思考分析，我认为这个问题需要从多个角度来考虑...';
    }

    if (options.online_search) {
      content = '根据最新搜索到的信息，当前的情况是...';
    }

    if (options.deep_thinking && options.online_search) {
      content = '结合最新信息和深度分析，我的综合建议是...';
    }

    res.json({
      success: true,
      data: {
        message: {
          role: 'assistant',
          content: content,
          id: messageId,
          timestamp: Date.now()
        },
        conversation_id: conversationId,
        message_id: messageId,
        request_id: 'req_' + Date.now(),
        model: model || 'deepseek-r1',
        finish_reason: 'stop'
      },
      requestId: 'req_' + Date.now(),
      timestamp: Date.now()
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: Date.now()
    }
  });
});

// 根路径重定向
app.get('/', (req, res) => {
  res.redirect('/chat');
});

// 启动服务器
app.listen(port, '0.0.0.0', () => {
  console.log('🎉 测试服务器启动成功!');
  console.log(`🌐 服务器地址: http://localhost:${port}`);
  console.log(`💬 聊天界面: http://localhost:${port}/chat`);
  console.log('🚀 服务器已就绪，等待请求...');
});
